<script lang="ts">
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import FloppyDiskIcon from 'phosphor-svelte/lib/FloppyDisk';
	import XIcon from 'phosphor-svelte/lib/X';
	import { Plus as PlusIcon } from 'phosphor-svelte';
	import { formatCurrency } from '$lib/utils.js';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import CaretUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import { tick } from 'svelte';
	import * as Command from '$lib/components/ui/command/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import { cn } from '$lib/utils.js';
	import { useId } from 'bits-ui';
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import { SvelteSet } from 'svelte/reactivity';
	import BudgetNode from '$lib/components/budget-node.svelte';
	import { calculateUnitRate, type BudgetLineItem } from '$lib/budget_utils.js';
	import { slide } from 'svelte/transition';
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import { useSidebar } from '$lib/components/ui/sidebar';

	const { data } = $props();

	const sidebar = useSidebar();

	const wbsItems = $derived(data.wbsItems);

	const form = superForm(data.form, {
		id: 'new-budget-item-form',
		onChange: async (event) => {
			if (
				event.paths.length === 1 &&
				[
					'unit_rate',
					'material_rate',
					'labor_rate',
					'productivity_per_hour',
					'unit_rate_manual_override',
				].includes(event.paths[0])
			) {
				$formData.unit_rate = calculateUnitRate($formData);
			}
		},
		onUpdated: async ({ form: f }) => {
			if (f.message) {
				if (f.message?.type === 'error') {
					toast.error(f.message?.text);
				} else if (f.message?.type === 'success') {
					toast.success(f.message?.text);
					cancelEditing();
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	let open = $state(false);
	let triggerRef = $state<HTMLButtonElement>(null!);

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string) {
		open = false;
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}
	const triggerId = useId();

	// const wbsOptions = $derived.by(() => {
	// 	return data.wbsItems.map((item) => ({
	// 		label: `${item.code}: ${item.description}`,
	// 		value: String(item.wbs_library_item_id),
	// 	}));
	// });

	function getWbsItemName(wbsId: string | null | undefined): string {
		if (!wbsId) return 'None';

		const item = data.wbsItems.find((item) => item.value === wbsId);
		return item ? item.label : 'Unknown';
	}

	// Props
	let projectId = $derived(data.project.project_id);

	// State
	let isAddingNewItem = $state(data.wbsTree.length === 0);
	let expandedNodeIds = new SvelteSet<string>();

	// Create an empty budget item
	function createEmptyBudgetItem(): BudgetLineItem {
		return {
			project_id: projectId,
			wbs_library_item_id: '',
			quantity: 0,
			unit: '',
			material_rate: 0,
			labor_rate: null,
			productivity_per_hour: null,
			unit_rate_manual_override: false,
			unit_rate: 0,
			remarks: null,
			cost_certainty: null,
			design_certainty: null,
		};
	}

	// Cancel editing
	function cancelEditing() {
		// Reset the form data
		$formData = createEmptyBudgetItem();
		isAddingNewItem = false;
	}

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}

	const isLoading = $derived(false);
</script>

<div class="overflow-hidden p-4">
	<div class="mb-4 flex items-center justify-end">
		<h1 class="sr-only">Project Budget</h1>
		<Button onclick={() => (isAddingNewItem = !isAddingNewItem)} class="flex items-center gap-2">
			<PlusIcon class="size-4" />
			<span>Add Budget Item</span>
		</Button>
	</div>

	{#if isLoading}
		<div class="flex justify-center p-8">
			<p>Loading budget data...</p>
		</div>
	{:else}
		<div
			class="-mr-4 -ml-4 overflow-scroll tabular-nums"
			style={`max-width: ${
				sidebar.open
					? 'calc(100vw - var(--sidebar-width) - 1rem)'
					: 'calc(100vw - var(--sidebar-width-icon) - 1rem)'
			}; max-height: calc(100vh - 11.5rem); position: relative;`}
		>
			<div class="budget grid auto-cols-min grid-cols-12 gap-0">
				<!-- Grid Header -->
				<div
					class="bg-muted col-span-[13] grid grid-cols-subgrid items-stretch border-t border-b py-2 pr-8 text-sm font-medium"
				>
					<div class="col-span-1 pr-2 pl-8">WBS Code</div>
					<div class="col-span-2 px-2">Description</div>
					<div class="col-span-1 px-2 text-right">Quantity</div>
					<div class="col-span-1 px-2">Unit</div>
					<div class="col-span-4 grid grid-cols-subgrid gap-1 text-center">
						<div class="col-span-4 border-b">Rate Calculation</div>
						<div class="col-span-1 px-2 text-right">Material Rate</div>
						<div class="col-span-1 px-2 text-right">Labor Rate</div>
						<div class="col-span-1 px-2 text-right">Productivity</div>
						<div class="col-span-1 px-2 text-right">Unit Rate</div>
					</div>
					<div class="col-span-1 px-2 text-right">Factor</div>
					<div class="col-span-1 px-2 text-right">Subtotal</div>
					<div class="col-span-1 px-2 text-right">Cost Certainty</div>
					<div class="col-span-1 px-2 text-right">Design Certainty</div>
				</div>
				{#if isAddingNewItem}
					<form
						action="?/upsertBudgetItem"
						method="post"
						use:enhance
						in:slide={{ duration: 300 }}
						class="bg-muted/50 col-span-[13] grid grid-cols-subgrid items-center pr-8"
					>
						<input type="hidden" name="project_id" value={projectId} />
						<input
							type="hidden"
							name="budget_line_item_id"
							value={$formData.budget_line_item_id || ''}
						/>
						<div class="col-span-3 px-2">
							<Form.Field {form} name="wbs_library_item_id" class="flex flex-col">
								<Popover.Root bind:open>
									<Form.Control id={triggerId}>
										{#snippet children({ props })}
											<Form.Label class="sr-only">WBS Code</Form.Label>
											<Popover.Trigger
												class={cn(
													buttonVariants({ variant: 'outline' }),
													'w-full justify-between',
													!$formData.wbs_library_item_id && 'text-muted-foreground',
												)}
												role="combobox"
												bind:ref={triggerRef}
												{...props}
											>
												{$formData.wbs_library_item_id
													? getWbsItemName($formData.wbs_library_item_id)
													: 'Select WBS code'}
												<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
											</Popover.Trigger>
											<input hidden value={$formData.wbs_library_item_id} name={props.name} />
										{/snippet}
									</Form.Control>
									<Popover.Content
										class="w-(--bits-popover-anchor-width) p-0"
										side="bottom"
										align="start"
									>
										<Command.Root>
											<Command.Input autofocus placeholder="Search parent items..." class="h-9" />
											<Command.Empty>No matching items found.</Command.Empty>
											<Command.Group class="max-h-[300px] overflow-y-auto">
												{#each wbsItems as option (option.value)}
													<Command.Item
														value={option.label}
														onSelect={() => {
															$formData.wbs_library_item_id = option.value;
															closeAndFocusTrigger(triggerId);
														}}
													>
														{option.label}
														<CheckIcon
															class={cn(
																'ml-auto size-4',
																option.value !== $formData.wbs_library_item_id &&
																	'text-transparent',
															)}
														/>
													</Command.Item>
												{/each}
											</Command.Group>
										</Command.Root>
									</Popover.Content>
								</Popover.Root>
								<!-- <Form.Description>
									Select a work breakdown structure item to associate with this budget line
								</Form.Description> -->
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="quantity" class="col-span-1 px-0">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Quantity</Form.Label>
										<Input
											{...props}
											type="number"
											step="0.01"
											bind:value={$formData.quantity}
											placeholder="Quantity"
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="unit">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Unit</Form.Label>
										<Input {...props} type="text" bind:value={$formData.unit} placeholder="m^2" />
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="material_rate">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Material Rate</Form.Label>
										<Input
											{...props}
											type="number"
											placeholder="Material Rate"
											step="0.01"
											bind:value={$formData.material_rate}
											disabled={$formData.unit_rate_manual_override}
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="labor_rate">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Labor Rate</Form.Label>
										<Input
											{...props}
											type="number"
											step="0.01"
											placeholder="0"
											bind:value={$formData.labor_rate}
											disabled={$formData.unit_rate_manual_override}
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="productivity_per_hour">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Productivity (qty/hour)</Form.Label>
										<Input
											{...props}
											type="number"
											step="0.01"
											bind:value={$formData.productivity_per_hour}
											disabled={$formData.unit_rate_manual_override}
											placeholder="0"
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="relative col-span-1 px-0">
							<div class="flex items-center gap-2">
								<Form.Field {form} name="unit_rate">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label class="sr-only">
												{$formData.unit_rate_manual_override
													? 'Manual Unit Rate'
													: 'Calculated Unit Rate'}
											</Form.Label>
											<Input
												{...props}
												type="number"
												step="0.01"
												bind:value={$formData.unit_rate}
												disabled={!$formData.unit_rate_manual_override}
												placeholder="0"
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</div>

							<Form.Field
								{form}
								name="unit_rate_manual_override"
								class="absolute -right-1 -bottom-8 z-10 flex min-w-max items-center gap-2"
							>
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label>Manual Unit Rate</Form.Label>
										<Checkbox {...props} bind:checked={$formData.unit_rate_manual_override} />
									{/snippet}
								</Form.Control>
							</Form.Field>
						</div>

						<div class="col-span-1 px-0">
							<Form.Field {form} name="factor">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Factor</Form.Label>
										<Input
											{...props}
											type="number"
											step="0.01"
											bind:value={$formData.factor}
											placeholder="1"
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 flex justify-end px-2 pt-1 leading-6">
							{formatCurrency($formData.quantity * ($formData.unit_rate || 0))}
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="cost_certainty">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Cost Certainty</Form.Label>
										<Input
											{...props}
											type="number"
											step="0.01"
											placeholder="%"
											bind:value={$formData.cost_certainty}
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-1 px-0">
							<Form.Field {form} name="design_certainty">
								<Form.Control>
									{#snippet children({ props })}
										<Form.Label class="sr-only">Design Certainty</Form.Label>
										<Input
											{...props}
											type="number"
											step="0.01"
											placeholder="%"
											bind:value={$formData.design_certainty}
										/>
									{/snippet}
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
						</div>
						<div class="col-span-[13] grid grid-cols-subgrid pt-6 pr-8 pb-2">
							<div class="col-span-11"></div>
							<div class="col-span-1">
								<Button variant="outline" class="mx-1 w-full" onclick={cancelEditing}>
									<XIcon class="mr-1 size-4" />
									Cancel
								</Button>
							</div>
							<div class="col-span-1">
								<Button class="mx-1 w-full" type="submit">
									<FloppyDiskIcon class="size-4" />
									Save
								</Button>
							</div>
						</div>
					</form>
				{/if}
				{#if data.wbsTree.length > 0}
					{#each data.wbsTree as rootNode (rootNode.nodeId)}
						<BudgetNode
							node={rootNode}
							indent={0}
							expanded={expandedNodeIds}
							toggle={toggleNodeExpanded}
							data={data.form}
							{projectId}
						/>
					{/each}
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	.budget {
		width: max-content;
		justify-items: stretch;
		align-items: start;

		& .col-span-1 {
			align-self: end;
			/* max-width: 6rem; */
			width: 6rem;
			/* width: 100%; */
		}
		& .col-span-2 {
			align-self: end;
			max-width: 12rem;
			/* width: 100%; */
		}

		& form :is(.col-span-1, .col-span-3) {
			align-self: start;
			margin-block-start: var(--spacing);
		}
	}

	.overflow-scroll {
		/* overscroll-behavior-x: contain; */
		padding-block: 0.5rem;
	}
	/*
	.overflow-x-auto::-webkit-scrollbar {
		height: 8px;
	}

	.overflow-x-auto::-webkit-scrollbar-track {
		background: transparent;
	}

	.overflow-x-auto::-webkit-scrollbar-thumb {
		background-color: var(--border);
		border-radius: 4px;
	} */
</style>
