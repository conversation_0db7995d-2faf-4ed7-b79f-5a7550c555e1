-- Create checklist_item_status enum
CREATE TYPE checklist_item_status AS ENUM('Incomplete', 'Deferred', 'Complete');

-- Create project stage table
CREATE TABLE IF NOT EXISTS "public"."project_stage" (
	project_stage_id bigint generated always as identity primary key,
	project_id uuid not null references project (project_id) on update restrict on delete restrict,
	name text not null,
	description text,
	stage_order integer not null,
	stage integer,
	gateway_qualitative_scorecard jsonb,
	date_started timestamptz,
	date_completed timestamptz,
	completion_notes text,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null -- allow reverting to a prior stage so do not make these unique
	-- unique (project_id, name),
	-- unique (project_id, stage_order)
);

comment on table "public"."project_stage" is 'Project stages tracking progress and completion status';

comment on column "public"."project_stage"."date_completed" is 'When a stage is completed, set this timestamp. Null indicates in-progress.';

-- Create gateway checklist item table
CREATE TABLE "public"."gateway_checklist_item" (
	gateway_checklist_item_id bigint generated always as identity primary key,
	project_stage_id bigint not null references project_stage (project_stage_id) on update restrict on delete restrict,
	name text not null,
	description text,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."gateway_checklist_item" is 'Checklist items that must be completed before a stage gateway can be signed off';

CREATE TABLE public.gateway_checklist_item_status_log (
	log_id bigint generated always as identity primary key,
	gateway_checklist_item_id BIGINT NOT NULL REFERENCES public.gateway_checklist_item (gateway_checklist_item_id) ON UPDATE RESTRICT ON DELETE CASCADE,
	status public.checklist_item_status not null default 'Incomplete',
	updated_by_user_id uuid not null default auth.uid () references profile (user_id) on update restrict on delete restrict,
	valid_at TIMESTAMPTZ NOT NULL DEFAULT now(),
	created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
	latest BOOLEAN NOT NULL DEFAULT FALSE
);

-- Speed up “find all statuses for item, newest first”
CREATE INDEX ON public.gateway_checklist_item_status_log (gateway_checklist_item_id, valid_at DESC);

-- Ensure exactly one “latest” row per item
CREATE UNIQUE INDEX ON public.gateway_checklist_item_status_log (gateway_checklist_item_id)
WHERE
	latest = TRUE;

comment on table "public"."gateway_checklist_item_status_log" is 'Log of status changes for checklist items';

CREATE OR REPLACE FUNCTION public.set_gateway_checklist_item_latest () RETURNS TRIGGER security definer
SET
	search_path = '' LANGUAGE plpgsql AS $$ BEGIN -- Un-flag the old latest
UPDATE public.gateway_checklist_item_status_log
SET latest = FALSE
WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
	AND latest = TRUE;
-- Flag the new newest row
UPDATE public.gateway_checklist_item_status_log
SET latest = TRUE
WHERE log_id = (
		SELECT log_id
		FROM public.gateway_checklist_item_status_log
		WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
		ORDER BY valid_at DESC
		LIMIT 1
	);
RETURN NULL;
-- suppress returning the row to caller
END;
$$;

CREATE TRIGGER trg_gateway_checklist_item_status_insert
AFTER INSERT ON public.gateway_checklist_item_status_log FOR EACH ROW
EXECUTE FUNCTION public.set_gateway_checklist_item_latest ();

-- Create budget snapshot tables
CREATE TABLE "public"."budget_snapshot" (
	budget_snapshot_id bigint generated always as identity primary key,
	project_stage_id bigint not null references project_stage (project_stage_id) on update restrict on delete restrict,
	freeze_date timestamptz not null default now(),
	freeze_reason text,
	created_by_user_id uuid not null references profile (user_id) on update restrict on delete restrict,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."budget_snapshot" is 'Frozen snapshots of project budgets at stage gateways';

CREATE TABLE "public"."budget_snapshot_line_item" (
	budget_snapshot_line_item_id bigint generated always as identity primary key,
	budget_snapshot_id bigint not null references budget_snapshot (budget_snapshot_id) on update restrict on delete restrict,
	wbs_library_item_id uuid not null references wbs_library_item (wbs_library_item_id) on update restrict on delete restrict,
	quantity numeric(15, 2),
	unit text,
	material_rate numeric(15, 2),
	labor_rate numeric(15, 2),
	productivity_per_hour numeric(15, 2),
	unit_rate_manual_override boolean not null default false,
	unit_rate numeric(15, 2),
	factor numeric(15, 2),
	remarks text,
	cost_certainty numeric(5, 2),
	design_certainty numeric(5, 2),
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."budget_snapshot_line_item" is 'Line items in a frozen budget snapshot';

-- Create incremental budget log tables
CREATE TABLE "public"."budget_line_item_current" (
	budget_line_item_id bigint generated always as identity primary key,
	project_id uuid not null references project (project_id) on update restrict on delete restrict,
	wbs_library_item_id uuid not null references wbs_library_item (wbs_library_item_id) on update restrict on delete restrict,
	quantity numeric(15, 2) not null,
	unit text,
	material_rate numeric(15, 2) not null,
	labor_rate numeric(15, 2),
	productivity_per_hour numeric(15, 2),
	unit_rate_manual_override boolean not null default false,
	unit_rate numeric(15, 2) not null,
	factor numeric(15, 2),
	remarks text,
	cost_certainty numeric(5, 2),
	design_certainty numeric(5, 2),
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."budget_line_item_current" is 'Current state of project budget line items';

-- Add indexes for performance
CREATE INDEX idx_budget_line_item_current_project_id ON budget_line_item_current (project_id);

CREATE INDEX idx_budget_line_item_current_wbs_library_item_id ON budget_line_item_current (wbs_library_item_id);

CREATE INDEX idx_budget_snapshot_line_item_wbs_library_item_id ON budget_snapshot_line_item (wbs_library_item_id);

-- Create trigger on budget_line_item_current to call the update function before any update
CREATE TRIGGER update_updated_at_blc BEFORE
UPDATE ON budget_line_item_current FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

-- Create trigger on project_stage to call the update function before any update
CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.project_stage FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

-- Create function to log initial checklist item status
CREATE OR REPLACE FUNCTION public.log_initial_checklist_item_status () RETURNS TRIGGER LANGUAGE plpgsql security definer
SET
	search_path = '' AS $$ BEGIN -- Insert a new status log entry for the newly created checklist item
INSERT INTO public.gateway_checklist_item_status_log (
		gateway_checklist_item_id,
		status,
		updated_by_user_id,
		valid_at,
		latest
	)
VALUES (
		NEW.gateway_checklist_item_id,
		'Incomplete',
		-- Default initial status
		auth.uid(),
		-- Current user who created the item
		now(),
		-- Current timestamp
		TRUE -- This is the latest status since it's the first one
	);
RETURN NULL;
-- Return value is ignored for AFTER triggers
END;
$$;

-- Create trigger to automatically log initial status when a checklist item is created
CREATE TRIGGER trg_gateway_checklist_item_insert
AFTER INSERT ON public.gateway_checklist_item FOR EACH ROW
EXECUTE FUNCTION public.log_initial_checklist_item_status ();

-- Create triggers for other tables
CREATE TRIGGER update_updated_at BEFORE
UPDATE ON gateway_checklist_item_status_log FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON gateway_checklist_item FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON budget_snapshot FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON budget_snapshot_line_item FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

-- Enable Row Level Security on all tables
ALTER TABLE project_stage ENABLE ROW LEVEL SECURITY;

ALTER TABLE gateway_checklist_item_status_log ENABLE ROW LEVEL SECURITY;

ALTER TABLE gateway_checklist_item ENABLE ROW LEVEL SECURITY;

ALTER TABLE budget_snapshot ENABLE ROW LEVEL SECURITY;

ALTER TABLE budget_snapshot_line_item ENABLE ROW LEVEL SECURITY;

ALTER TABLE budget_line_item_current ENABLE ROW LEVEL SECURITY;

-- Legacy audit table RLS removed - handled by modern audit system
-- Grant access to service_role for all tables
GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON ALL TABLES IN SCHEMA public TO service_role;

-- Setup policies for project_stage
CREATE POLICY "Project Editors and Owners can insert project stage" ON project_stage FOR INSERT TO authenticated
WITH
	CHECK (
		(
			SELECT
				can_modify_project (project_id)
		)
	);

CREATE POLICY "Project Editors and Owners can update project stage" ON project_stage
FOR UPDATE
	TO authenticated USING (
		(
			SELECT
				can_modify_project (project_id)
		)
	);

CREATE POLICY "Project Editors and Owners can delete project stage" ON project_stage FOR DELETE TO authenticated USING (
	(
		SELECT
			can_modify_project (project_id)
	)
);

CREATE POLICY "Project Viewers, Editors, and Owners can view project stage" ON project_stage FOR
SELECT
	TO authenticated USING (
		(
			SELECT
				can_access_project (project_id)
		)
	);

-- Gateway checklist item policies
CREATE POLICY "Project Editors and Owners can insert gateway checklist item" ON gateway_checklist_item FOR INSERT TO authenticated
WITH
	CHECK (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = gateway_checklist_item.project_stage_id
					)
				)
		)
	);

CREATE POLICY "Project Editors and Owners can update gateway checklist item" ON gateway_checklist_item
FOR UPDATE
	TO authenticated USING (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = gateway_checklist_item.project_stage_id
					)
				)
		)
	);

CREATE POLICY "Project Editors and Owners can delete gateway checklist item" ON gateway_checklist_item FOR DELETE TO authenticated USING (
	(
		SELECT
			can_modify_project (
				(
					SELECT
						project_id
					FROM
						project_stage
					WHERE
						project_stage_id = gateway_checklist_item.project_stage_id
				)
			)
	)
);

CREATE POLICY "Project viewers can view gateway checklist item" ON gateway_checklist_item FOR
SELECT
	TO authenticated USING (
		(
			SELECT
				can_access_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = gateway_checklist_item.project_stage_id
					)
				)
		)
	);

-- checklist item status log policy
CREATE POLICY "Project viewers can view gateway checklist item status log" ON gateway_checklist_item_status_log FOR
SELECT
	TO authenticated USING (
		(
			SELECT
				can_access_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = (
								SELECT
									project_stage_id
								FROM
									gateway_checklist_item
								WHERE
									gateway_checklist_item_id = gateway_checklist_item_status_log.gateway_checklist_item_id
							)
					)
				)
		)
	);

-- Budget snapshot policies
CREATE POLICY "Project viewers can insert budget snapshot" ON budget_snapshot FOR INSERT TO authenticated
WITH
	CHECK (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = budget_snapshot.project_stage_id
					)
				)
		)
	);

CREATE POLICY "Project editors can update budget snapshot" ON budget_snapshot
FOR UPDATE
	TO authenticated USING (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = budget_snapshot.project_stage_id
					)
				)
		)
	);

CREATE POLICY "Project editors can delete budget snapshot" ON budget_snapshot FOR DELETE TO authenticated USING (
	(
		SELECT
			can_modify_project (
				(
					SELECT
						project_id
					FROM
						project_stage
					WHERE
						project_stage_id = budget_snapshot.project_stage_id
				)
			)
	)
);

CREATE POLICY "Project viewers can view budget snapshot" ON budget_snapshot FOR
SELECT
	TO authenticated USING (
		(
			SELECT
				can_access_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = budget_snapshot.project_stage_id
					)
				)
		)
	);

-- Budget snapshot line item policies
CREATE POLICY "Project editors can insert budget snapshot line item" ON budget_snapshot_line_item FOR INSERT TO authenticated
WITH
	CHECK (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = (
								SELECT
									project_stage_id
								FROM
									budget_snapshot
								WHERE
									budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id
							)
					)
				)
		)
	);

CREATE POLICY "Project editors can update budget snapshot line item" ON budget_snapshot_line_item
FOR UPDATE
	TO authenticated USING (
		(
			SELECT
				can_modify_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = (
								SELECT
									project_stage_id
								FROM
									budget_snapshot
								WHERE
									budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id
							)
					)
				)
		)
	);

CREATE POLICY "Project editors can delete budget snapshot line item" ON budget_snapshot_line_item FOR DELETE TO authenticated USING (
	(
		SELECT
			can_modify_project (
				(
					SELECT
						project_id
					FROM
						project_stage
					WHERE
						project_stage_id = (
							SELECT
								project_stage_id
							FROM
								budget_snapshot
							WHERE
								budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id
						)
				)
			)
	)
);

CREATE POLICY "Project viewers can view budget snapshot line item" ON budget_snapshot_line_item FOR
SELECT
	TO authenticated USING (
		(
			SELECT
				can_access_project (
					(
						SELECT
							project_id
						FROM
							project_stage
						WHERE
							project_stage_id = (
								SELECT
									project_stage_id
								FROM
									budget_snapshot
								WHERE
									budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id
							)
					)
				)
		)
	);

-- Budget line item current policies
CREATE POLICY "Project editors can insert budget line item" ON budget_line_item_current FOR INSERT TO authenticated
WITH
	CHECK (
		(
			SELECT
				can_modify_project (project_id)
		)
	);

CREATE POLICY "Project editors can update budget line item" ON budget_line_item_current
FOR UPDATE
	TO authenticated USING (
		(
			SELECT
				can_modify_project (project_id)
		)
	);

CREATE POLICY "Project editors can delete budget line item" ON budget_line_item_current FOR DELETE TO authenticated USING (
	(
		SELECT
			can_modify_project (project_id)
	)
);

CREATE POLICY "Project viewers can view budget line item" ON budget_line_item_current FOR
SELECT
	TO authenticated USING (
		(
			SELECT
				can_access_project (project_id)
		)
	);

-- Legacy budget line item audit policies removed - handled by modern audit system
